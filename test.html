<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .test-item {
            margin-bottom: 15px;
            padding: 10px;
            border-left: 4px solid #409eff;
            background-color: #f8f9fa;
        }
        .test-result {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .success {
            background-color: #f0f9ff;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        .error {
            background-color: #fff5f5;
            color: #d32f2f;
            border: 1px solid #ffcdd2;
        }
        .warning {
            background-color: #fffbf0;
            color: #f57c00;
            border: 1px solid #ffe0b2;
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #337ab7;
        }
        .main-link {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
        }
        .main-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>大理州网络故障地图可视化系统 - 功能测试</h1>
        
        <div class="test-item">
            <h3>1. 基础环境检测</h3>
            <button onclick="testEnvironment()">检测环境</button>
            <div id="env-result" class="test-result"></div>
        </div>

        <div class="test-item">
            <h3>2. 数据处理功能测试</h3>
            <button onclick="testDataProcessing()">测试数据处理</button>
            <div id="data-result" class="test-result"></div>
        </div>

        <div class="test-item">
            <h3>3. 地图功能测试</h3>
            <button onclick="testMapFunctions()">测试地图功能</button>
            <div id="map-result" class="test-result"></div>
        </div>

        <div class="test-item">
            <h3>4. CSV解析测试</h3>
            <button onclick="testCSVParsing()">测试CSV解析</button>
            <div id="csv-result" class="test-result"></div>
        </div>

        <div class="test-item">
            <h3>5. 筛选功能测试</h3>
            <button onclick="testFilterFunctions()">测试筛选功能</button>
            <div id="filter-result" class="test-result"></div>
        </div>

        <a href="index.html" class="main-link">🚀 启动主系统</a>
    </div>

    <script>
        // 测试环境检测
        function testEnvironment() {
            const result = document.getElementById('env-result');
            let tests = [];
            
            // 检测浏览器支持
            tests.push({
                name: '现代浏览器支持',
                passed: typeof Promise !== 'undefined' && typeof fetch !== 'undefined',
                message: typeof Promise !== 'undefined' && typeof fetch !== 'undefined' ? 
                    '浏览器支持现代JavaScript特性' : '浏览器版本过低，建议升级'
            });
            
            // 检测本地存储
            tests.push({
                name: '本地存储支持',
                passed: typeof localStorage !== 'undefined',
                message: typeof localStorage !== 'undefined' ? 
                    '支持本地数据存储' : '不支持本地存储'
            });
            
            // 检测文件API
            tests.push({
                name: '文件API支持',
                passed: typeof FileReader !== 'undefined',
                message: typeof FileReader !== 'undefined' ? 
                    '支持文件上传和解析' : '不支持文件操作'
            });
            
            displayTestResults(result, tests);
        }

        // 测试数据处理功能
        function testDataProcessing() {
            const result = document.getElementById('data-result');
            let tests = [];
            
            try {
                // 测试示例数据
                const sampleData = [
                    {
                        area: '大理市',
                        longitude: 100.203212,
                        latitude: 25.593299,
                        level: '接入层',
                        system: 'PTN GE环',
                        time: '2025/1/1 11:50',
                        cause: '车辆挂断',
                        detail: '测试数据'
                    }
                ];
                
                tests.push({
                    name: '数据结构验证',
                    passed: sampleData.length > 0 && sampleData[0].area,
                    message: '数据结构正确'
                });
                
                // 测试坐标验证
                const validCoords = sampleData[0].longitude >= 99 && 
                                  sampleData[0].longitude <= 102 &&
                                  sampleData[0].latitude >= 24 && 
                                  sampleData[0].latitude <= 27;
                
                tests.push({
                    name: '坐标范围验证',
                    passed: validCoords,
                    message: validCoords ? '坐标在大理州范围内' : '坐标超出范围'
                });
                
            } catch (error) {
                tests.push({
                    name: '数据处理异常',
                    passed: false,
                    message: '数据处理出现错误: ' + error.message
                });
            }
            
            displayTestResults(result, tests);
        }

        // 测试地图功能
        function testMapFunctions() {
            const result = document.getElementById('map-result');
            let tests = [];
            
            // 检测地图容器
            tests.push({
                name: '地图容器检测',
                passed: true,
                message: '模拟地图模式可用'
            });
            
            // 检测标记功能
            tests.push({
                name: '标记功能',
                passed: typeof document.createElement === 'function',
                message: '支持动态标记创建'
            });
            
            displayTestResults(result, tests);
        }

        // 测试CSV解析
        function testCSVParsing() {
            const result = document.getElementById('csv-result');
            let tests = [];
            
            const testCSV = `区域,故障级别,经度,纬度
大理市,接入层,100.203212,25.593299
宾川县,汇聚层,100.451122,25.771616`;
            
            try {
                // 模拟CSV解析
                const lines = testCSV.split('\n');
                const headers = lines[0].split(',');
                const dataRows = lines.slice(1);
                
                tests.push({
                    name: 'CSV格式解析',
                    passed: headers.length > 0 && dataRows.length > 0,
                    message: `成功解析 ${dataRows.length} 行数据`
                });
                
                // 测试数据转换
                const parsedData = dataRows.map(row => {
                    const values = row.split(',');
                    return {
                        area: values[0],
                        level: values[1],
                        longitude: parseFloat(values[2]),
                        latitude: parseFloat(values[3])
                    };
                });
                
                tests.push({
                    name: '数据类型转换',
                    passed: !isNaN(parsedData[0].longitude) && !isNaN(parsedData[0].latitude),
                    message: '坐标数据转换正确'
                });
                
            } catch (error) {
                tests.push({
                    name: 'CSV解析异常',
                    passed: false,
                    message: 'CSV解析失败: ' + error.message
                });
            }
            
            displayTestResults(result, tests);
        }

        // 测试筛选功能
        function testFilterFunctions() {
            const result = document.getElementById('filter-result');
            let tests = [];
            
            const testData = [
                { area: '大理市', level: '接入层', cause: '车辆挂断' },
                { area: '宾川县', level: '汇聚层', cause: '人为破坏' },
                { area: '大理市', level: '汇聚层', cause: '车辆挂断' }
            ];
            
            // 测试区域筛选
            const areaFiltered = testData.filter(item => item.area === '大理市');
            tests.push({
                name: '区域筛选',
                passed: areaFiltered.length === 2,
                message: `区域筛选正确，筛选出 ${areaFiltered.length} 条记录`
            });
            
            // 测试级别筛选
            const levelFiltered = testData.filter(item => item.level === '汇聚层');
            tests.push({
                name: '级别筛选',
                passed: levelFiltered.length === 2,
                message: `级别筛选正确，筛选出 ${levelFiltered.length} 条记录`
            });
            
            // 测试原因筛选
            const causeFiltered = testData.filter(item => item.cause === '车辆挂断');
            tests.push({
                name: '原因筛选',
                passed: causeFiltered.length === 2,
                message: `原因筛选正确，筛选出 ${causeFiltered.length} 条记录`
            });
            
            displayTestResults(result, tests);
        }

        // 显示测试结果
        function displayTestResults(container, tests) {
            const passedCount = tests.filter(t => t.passed).length;
            const totalCount = tests.length;
            
            let html = `<div class="${passedCount === totalCount ? 'success' : 'warning'}">
                <strong>测试结果: ${passedCount}/${totalCount} 通过</strong>
            </div>`;
            
            tests.forEach(test => {
                html += `<div class="${test.passed ? 'success' : 'error'}" style="margin-top: 5px;">
                    ${test.passed ? '✅' : '❌'} ${test.name}: ${test.message}
                </div>`;
            });
            
            container.innerHTML = html;
        }

        // 页面加载完成后自动运行基础检测
        window.onload = function() {
            console.log('大理州网络故障地图可视化系统 - 测试页面已加载');
        };
    </script>
</body>
</html>
