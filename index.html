<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大理州网络故障地图可视化系统</title>
    
    <!-- 引入Element Plus样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- 引入自定义样式 -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- 百度地图API - 演示模式，如需完整地图功能请配置有效的API密钥 -->
    <!-- <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=您的百度地图API密钥"></script> -->
</head>
<body>
    <div id="app">
        <!-- 头部标题 -->
        <header class="header">
            <h1>大理州网络故障地图可视化系统</h1>
            <p>网络故障分析与可视化演示平台</p>
        </header>

        <!-- 主要内容区域 -->
        <div class="main-container">
            <!-- 左侧控制面板 -->
            <div class="control-panel">
                <!-- 数据导入区域 -->
                <el-card class="card-section" header="数据导入">
                    <el-upload
                        class="upload-demo"
                        drag
                        action=""
                        :auto-upload="false"
                        :on-change="handleFileChange"
                        accept=".csv,.xlsx,.xls">
                        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                        <div class="el-upload__text">
                            将文件拖到此处，或<em>点击上传</em>
                        </div>
                        <template #tip>
                            <div class="el-upload__tip">
                                支持CSV、Excel格式文件
                            </div>
                        </template>
                    </el-upload>
                </el-card>

                <!-- 筛选控制区域 -->
                <el-card class="card-section" header="数据筛选">
                    <!-- 区域筛选 -->
                    <div class="filter-item">
                        <label>区域：</label>
                        <el-select v-model="filters.area" placeholder="选择区域" clearable>
                            <el-option
                                v-for="area in areaOptions"
                                :key="area"
                                :label="area"
                                :value="area">
                            </el-option>
                        </el-select>
                    </div>

                    <!-- 故障级别筛选 -->
                    <div class="filter-item">
                        <label>故障级别：</label>
                        <el-checkbox-group v-model="filters.level">
                            <el-checkbox label="汇聚层">汇聚层</el-checkbox>
                            <el-checkbox label="接入层">接入层</el-checkbox>
                        </el-checkbox-group>
                    </div>

                    <!-- 故障原因筛选 -->
                    <div class="filter-item">
                        <label>故障原因：</label>
                        <el-select v-model="filters.cause" placeholder="选择故障原因" clearable>
                            <el-option
                                v-for="cause in causeOptions"
                                :key="cause"
                                :label="cause"
                                :value="cause">
                            </el-option>
                        </el-select>
                    </div>

                    <!-- 关键词搜索 -->
                    <div class="filter-item">
                        <label>关键词搜索：</label>
                        <el-input
                            v-model="filters.keyword"
                            placeholder="搜索故障段落或详细原因"
                            clearable>
                        </el-input>
                    </div>

                    <!-- 筛选按钮 -->
                    <div class="filter-buttons">
                        <el-button type="primary" @click="applyFilters">应用筛选</el-button>
                        <el-button @click="resetFilters">重置</el-button>
                    </div>
                </el-card>

                <!-- 统计信息区域 -->
                <el-card class="card-section" header="统计信息">
                    <div class="stats-item">
                        <span class="stats-label">总故障数：</span>
                        <span class="stats-value">{{ statistics.total }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">汇聚层故障：</span>
                        <span class="stats-value stats-critical">{{ statistics.critical }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">接入层故障：</span>
                        <span class="stats-value stats-warning">{{ statistics.warning }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">车辆挂断：</span>
                        <span class="stats-value">{{ statistics.vehicle }}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">人为破坏：</span>
                        <span class="stats-value">{{ statistics.human }}</span>
                    </div>
                </el-card>
            </div>

            <!-- 右侧地图区域 -->
            <div class="map-container">
                <div id="map" class="map"></div>
            </div>
        </div>

        <!-- 故障详情弹窗 -->
        <el-dialog
            v-model="dialogVisible"
            title="故障详细信息"
            width="600px">
            <div v-if="selectedFault" class="fault-detail">
                <div class="detail-row">
                    <span class="detail-label">区域：</span>
                    <span class="detail-value">{{ selectedFault.area }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">故障级别：</span>
                    <span class="detail-value" :class="selectedFault.level === '汇聚层' ? 'level-critical' : 'level-warning'">
                        {{ selectedFault.level }}
                    </span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">故障段落：</span>
                    <span class="detail-value">{{ selectedFault.system }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">故障时间：</span>
                    <span class="detail-value">{{ selectedFault.time }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">故障原因：</span>
                    <span class="detail-value">{{ selectedFault.cause }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">详细原因：</span>
                    <span class="detail-value">{{ selectedFault.detail }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">坐标位置：</span>
                    <span class="detail-value">{{ selectedFault.longitude }}, {{ selectedFault.latitude }}</span>
                </div>
            </div>
        </el-dialog>
    </div>

    <!-- 引入Vue.js和Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>
    
    <!-- 引入自定义脚本 -->
    <script src="js/data.js"></script>
    <script src="js/map.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
