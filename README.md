# 大理州网络故障地图可视化系统

## 项目简介

这是一个基于Web技术开发的网络故障地图可视化系统，专门用于大理州地区的网络故障数据分析和展示。系统支持故障数据的地图标记、多维度筛选、统计分析和数据导入功能，适用于PPT演示和故障分析工作。

## 功能特性

### 🗺️ 地图可视化
- 基于百度地图API的交互式地图
- 故障点位置精确标记
- 不同故障级别的颜色区分（汇聚层-红色，接入层-橙色）
- 点击标记查看故障详情
- 自动调整地图视野以包含所有故障点

### 🔍 多维度筛选
- **区域筛选**：按行政区域过滤故障数据
- **故障级别筛选**：汇聚层/接入层复选框筛选
- **故障原因筛选**：车辆挂断、人为破坏、市政施工等
- **关键词搜索**：在故障段落和详细原因中搜索
- **一键重置**：快速清除所有筛选条件

### 📊 统计分析
- 实时统计总故障数量
- 按故障级别分类统计
- 按故障原因分类统计
- 动态更新统计数据

### 📁 数据管理
- 支持CSV格式文件上传
- 自动解析故障数据
- 数据验证和格式化
- 示例数据预加载

### 💻 用户界面
- 响应式设计，支持不同屏幕尺寸
- 现代化UI组件（基于Element Plus）
- 直观的操作界面
- 详细的故障信息弹窗

## 技术架构

### 前端技术栈
- **Vue.js 3.x** - 响应式前端框架
- **Element Plus** - UI组件库
- **百度地图API** - 地图服务
- **Papa Parse** - CSV文件解析
- **CSS3** - 样式和动画

### 项目结构
```
地图ppt2/
├── index.html          # 主页面
├── css/
│   └── style.css       # 样式文件
├── js/
│   ├── app.js          # Vue应用主逻辑
│   ├── map.js          # 地图功能模块
│   └── data.js         # 数据处理模块
├── data/
│   └── sample-data.json # 示例数据
└── README.md           # 使用说明
```

## 快速开始

### 1. 环境准备
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 网络连接（用于加载CDN资源）

### 2. 配置百度地图API（可选）
1. 访问 [百度地图开放平台](https://lbsyun.baidu.com/)
2. 注册账号并创建应用
3. 获取API密钥
4. 在 `index.html` 中替换API密钥：
   ```html
   <script src="https://api.map.baidu.com/api?v=3.0&ak=您的百度地图API密钥"></script>
   ```

### 3. 启动系统
1. 将项目文件放置在Web服务器目录中
2. 通过浏览器访问 `index.html`
3. 系统将自动加载示例数据并显示地图

### 4. 数据导入
1. 点击左侧"数据导入"区域
2. 拖拽或选择CSV文件
3. 系统自动解析并显示在地图上

## 数据格式说明

### CSV文件格式
系统支持以下CSV文件格式：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 区域 | 故障发生的行政区域 | 宾川县 |
| 故障系统（断点经纬度） | 包含经纬度信息的字段 | 经度：100.451122<br>纬度：25.771616 |
| 故障级别 | 故障的网络层级 | 汇聚层/接入层 |
| 故障段落 | 具体的故障系统描述 | SPN 100G 环 宾川县... |
| 故障发生时间 | 故障发生的时间 | 2025/1/1 11:50 |
| 故障原因 | 故障的主要原因 | 车辆挂断 |
| 故障详细原因备注 | 详细的故障描述 | 瑶草庄出局3.2km挖机挂断光缆 |

### 坐标格式
经纬度信息应包含在"故障系统（断点经纬度）"字段中，格式如下：
```
经度：100.451122
纬度：25.771616
```

## 使用指南

### 基本操作
1. **查看故障分布**：系统启动后自动显示所有故障点
2. **筛选数据**：使用左侧筛选面板按需过滤数据
3. **查看详情**：点击地图上的故障标记查看详细信息
4. **统计分析**：查看左侧统计面板了解故障分布情况

### 高级功能
1. **数据导入**：上传自定义CSV文件替换示例数据
2. **组合筛选**：同时使用多个筛选条件精确定位故障
3. **关键词搜索**：在故障描述中搜索特定内容

## 演示模式

当百度地图API不可用时，系统会自动切换到演示模式：
- 显示模拟地图界面
- 故障数据以卡片形式展示
- 保持所有筛选和统计功能
- 适用于离线演示场景

## 浏览器兼容性

| 浏览器 | 最低版本 | 状态 |
|--------|----------|------|
| Chrome | 80+ | ✅ 完全支持 |
| Firefox | 75+ | ✅ 完全支持 |
| Safari | 13+ | ✅ 完全支持 |
| Edge | 80+ | ✅ 完全支持 |
| IE | - | ❌ 不支持 |

## 故障排除

### 常见问题

**Q: 地图无法显示？**
A: 检查网络连接和百度地图API密钥配置，或使用演示模式。

**Q: CSV文件上传失败？**
A: 确保文件格式正确，包含必需的字段，特别是经纬度信息。

**Q: 故障点不在地图上显示？**
A: 检查坐标数据是否在大理州范围内（经度99-101.5，纬度24.5-27）。

**Q: 筛选功能不工作？**
A: 刷新页面重新加载，确保数据已正确导入。

### 技术支持

如遇到技术问题，请检查：
1. 浏览器控制台是否有错误信息
2. 网络连接是否正常
3. 数据格式是否符合要求

## 更新日志

### v1.0.0 (2025-01-08)
- 初始版本发布
- 基础地图可视化功能
- 多维度数据筛选
- CSV数据导入支持
- 响应式用户界面

## 许可证

本项目仅用于演示和学习目的。
