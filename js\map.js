// 地图相关功能模块
class MapManager {
    constructor() {
        this.map = null;
        this.markers = [];
        this.infoWindow = null;
        this.isMapReady = false;
    }

    // 初始化地图
    initMap() {
        return new Promise((resolve, reject) => {
            try {
                // 检查百度地图API是否加载
                if (typeof BMap === 'undefined') {
                    console.warn('百度地图API未加载，使用模拟地图');
                    this.initMockMap();
                    resolve();
                    return;
                }

                // 创建地图实例
                this.map = new BMap.Map("map");
                
                // 设置大理州中心点坐标
                const daliCenter = new BMap.Point(100.2, 25.6);
                
                // 初始化地图，设置中心点坐标和地图级别
                this.map.centerAndZoom(daliCenter, 10);
                
                // 启用滚轮缩放
                this.map.enableScrollWheelZoom(true);
                
                // 添加地图控件
                this.map.addControl(new BMap.NavigationControl());
                this.map.addControl(new BMap.ScaleControl());
                this.map.addControl(new BMap.OverviewMapControl());
                this.map.addControl(new BMap.MapTypeControl());
                
                // 创建信息窗口
                this.infoWindow = new BMap.InfoWindow("", {
                    width: 300,
                    height: 200,
                    title: "故障详情"
                });
                
                this.isMapReady = true;
                console.log('百度地图初始化成功');
                resolve();
                
            } catch (error) {
                console.error('地图初始化失败:', error);
                this.initMockMap();
                resolve();
            }
        });
    }

    // 模拟地图（当百度地图API不可用时）
    initMockMap() {
        const mapElement = document.getElementById('map');
        mapElement.innerHTML = `
            <div style="
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                color: #1976d2;
                font-size: 18px;
                text-align: center;
                flex-direction: column;
            ">
                <div style="margin-bottom: 20px;">
                    <i class="el-icon-location" style="font-size: 48px; margin-bottom: 10px;"></i>
                    <div>大理州网络故障地图</div>
                    <div style="font-size: 14px; margin-top: 10px; opacity: 0.8;">
                        演示模式 - 请配置百度地图API密钥以启用完整地图功能
                    </div>
                </div>
                <div id="mock-markers" style="
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 10px;
                    width: 80%;
                    max-width: 600px;
                "></div>
            </div>
        `;
        this.isMapReady = true;
        console.log('模拟地图初始化成功');
    }

    // 添加故障标记点
    addMarkers(faultData) {
        if (!this.isMapReady) {
            console.warn('地图未准备就绪');
            return;
        }

        // 清除现有标记
        this.clearMarkers();

        if (typeof BMap === 'undefined') {
            this.addMockMarkers(faultData);
            return;
        }

        faultData.forEach((fault, index) => {
            try {
                // 创建标记点
                const point = new BMap.Point(fault.longitude, fault.latitude);
                
                // 根据故障级别选择图标
                const iconUrl = fault.level === '汇聚层' 
                    ? this.createMarkerIcon('#f56c6c') 
                    : this.createMarkerIcon('#e6a23c');
                
                const icon = new BMap.Icon(iconUrl, new BMap.Size(20, 20));
                const marker = new BMap.Marker(point, { icon: icon });
                
                // 添加点击事件
                marker.addEventListener('click', () => {
                    this.showFaultInfo(fault, marker);
                });
                
                // 添加到地图
                this.map.addOverlay(marker);
                this.markers.push(marker);
                
            } catch (error) {
                console.error('添加标记失败:', error, fault);
            }
        });

        console.log(`成功添加 ${this.markers.length} 个故障标记`);
    }

    // 添加模拟标记（演示模式）
    addMockMarkers(faultData) {
        const mockMarkersContainer = document.getElementById('mock-markers');
        if (!mockMarkersContainer) return;

        mockMarkersContainer.innerHTML = '';
        
        faultData.forEach((fault, index) => {
            const markerElement = document.createElement('div');
            markerElement.style.cssText = `
                background: white;
                border-radius: 8px;
                padding: 10px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                cursor: pointer;
                transition: transform 0.2s ease;
                border-left: 4px solid ${fault.level === '汇聚层' ? '#f56c6c' : '#e6a23c'};
            `;
            
            markerElement.innerHTML = `
                <div style="font-weight: 600; color: #303133; margin-bottom: 5px;">
                    ${fault.area} - ${fault.level}
                </div>
                <div style="font-size: 12px; color: #909399;">
                    ${fault.time}
                </div>
            `;
            
            markerElement.addEventListener('click', () => {
                // 触发Vue应用的故障详情显示
                if (window.vueApp && window.vueApp.showFaultDetail) {
                    window.vueApp.showFaultDetail(fault);
                }
            });
            
            markerElement.addEventListener('mouseenter', () => {
                markerElement.style.transform = 'translateY(-2px)';
            });
            
            markerElement.addEventListener('mouseleave', () => {
                markerElement.style.transform = 'translateY(0)';
            });
            
            mockMarkersContainer.appendChild(markerElement);
        });
    }

    // 创建标记图标
    createMarkerIcon(color) {
        const canvas = document.createElement('canvas');
        canvas.width = 20;
        canvas.height = 20;
        const ctx = canvas.getContext('2d');
        
        // 绘制圆形标记
        ctx.beginPath();
        ctx.arc(10, 10, 8, 0, 2 * Math.PI);
        ctx.fillStyle = color;
        ctx.fill();
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        return canvas.toDataURL();
    }

    // 显示故障信息窗口
    showFaultInfo(fault, marker) {
        if (!this.infoWindow) return;

        const content = `
            <div style="padding: 10px; font-size: 12px;">
                <div style="font-weight: 600; margin-bottom: 8px; color: #303133;">
                    ${fault.area} - ${fault.level}
                </div>
                <div style="margin-bottom: 5px;">
                    <span style="color: #909399;">故障时间：</span>
                    <span>${fault.time}</span>
                </div>
                <div style="margin-bottom: 5px;">
                    <span style="color: #909399;">故障原因：</span>
                    <span>${fault.cause}</span>
                </div>
                <div style="margin-bottom: 8px;">
                    <span style="color: #909399;">故障段落：</span>
                    <span style="word-break: break-all;">${fault.system.substring(0, 50)}${fault.system.length > 50 ? '...' : ''}</span>
                </div>
                <div style="text-align: center;">
                    <button onclick="window.vueApp.showFaultDetail(${JSON.stringify(fault).replace(/"/g, '&quot;')})" 
                            style="background: #409eff; color: white; border: none; padding: 5px 15px; border-radius: 4px; cursor: pointer;">
                        查看详情
                    </button>
                </div>
            </div>
        `;
        
        this.infoWindow.setContent(content);
        this.map.openInfoWindow(this.infoWindow, marker.getPosition());
    }

    // 清除所有标记
    clearMarkers() {
        if (typeof BMap !== 'undefined' && this.map) {
            this.markers.forEach(marker => {
                this.map.removeOverlay(marker);
            });
        }
        
        // 清除模拟标记
        const mockMarkersContainer = document.getElementById('mock-markers');
        if (mockMarkersContainer) {
            mockMarkersContainer.innerHTML = '';
        }
        
        this.markers = [];
    }

    // 调整地图视野以包含所有标记
    fitView(faultData) {
        if (typeof BMap === 'undefined' || !this.map || faultData.length === 0) {
            return;
        }

        try {
            const points = faultData.map(fault => 
                new BMap.Point(fault.longitude, fault.latitude)
            );
            
            if (points.length === 1) {
                this.map.centerAndZoom(points[0], 12);
            } else {
                this.map.setViewport(points);
            }
        } catch (error) {
            console.error('调整地图视野失败:', error);
        }
    }

    // 获取地图状态
    getMapStatus() {
        return {
            isReady: this.isMapReady,
            markerCount: this.markers.length,
            hasRealMap: typeof BMap !== 'undefined'
        };
    }
}

// 导出地图管理器实例
window.mapManager = new MapManager();
