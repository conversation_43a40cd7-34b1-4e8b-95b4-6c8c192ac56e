/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f7fa;
    color: #333;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 20px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 28px;
    margin-bottom: 8px;
    font-weight: 600;
}

.header p {
    font-size: 14px;
    opacity: 0.9;
}

/* 主容器布局 */
.main-container {
    display: flex;
    height: calc(100vh - 120px);
    gap: 20px;
    padding: 20px;
}

/* 左侧控制面板 */
.control-panel {
    width: 350px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    overflow-y: auto;
    padding: 20px;
}

.card-section {
    margin-bottom: 20px;
}

.card-section:last-child {
    margin-bottom: 0;
}

/* 筛选项样式 */
.filter-item {
    margin-bottom: 15px;
}

.filter-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #606266;
}

.filter-item .el-select,
.filter-item .el-input {
    width: 100%;
}

.filter-item .el-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 筛选按钮 */
.filter-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.filter-buttons .el-button {
    flex: 1;
}

/* 统计信息样式 */
.stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stats-item:last-child {
    border-bottom: none;
}

.stats-label {
    font-size: 14px;
    color: #606266;
}

.stats-value {
    font-size: 16px;
    font-weight: 600;
    color: #409eff;
}

.stats-critical {
    color: #f56c6c;
}

.stats-warning {
    color: #e6a23c;
}

/* 右侧地图容器 */
.map-container {
    flex: 1;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    overflow: hidden;
}

.map {
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

/* 上传组件样式 */
.upload-demo {
    width: 100%;
}

.upload-demo .el-upload {
    width: 100%;
}

.upload-demo .el-upload-dragger {
    width: 100%;
    height: 120px;
}

/* 故障详情弹窗样式 */
.fault-detail {
    padding: 10px 0;
}

.detail-row {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-label {
    width: 100px;
    font-weight: 500;
    color: #606266;
    flex-shrink: 0;
}

.detail-value {
    flex: 1;
    color: #303133;
    word-break: break-all;
}

.level-critical {
    color: #f56c6c;
    font-weight: 600;
}

.level-warning {
    color: #e6a23c;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .control-panel {
        width: 300px;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
        height: auto;
        min-height: calc(100vh - 120px);
    }
    
    .control-panel {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .map-container {
        height: 500px;
    }
    
    .header h1 {
        font-size: 24px;
    }
    
    .filter-buttons {
        flex-direction: column;
    }
    
    .filter-buttons .el-button {
        width: 100%;
    }
}

/* 地图标记样式 */
.map-marker {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.map-marker:hover {
    transform: scale(1.2);
}

.marker-critical {
    background-color: #f56c6c;
}

.marker-warning {
    background-color: #e6a23c;
}

/* 加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #909399;
}

/* 滚动条样式 */
.control-panel::-webkit-scrollbar {
    width: 6px;
}

.control-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Element Plus 组件样式覆盖 */
.el-card__header {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #303133;
}

.el-dialog__header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #ebeef5;
}

.el-upload-dragger:hover {
    border-color: #409eff;
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
}
