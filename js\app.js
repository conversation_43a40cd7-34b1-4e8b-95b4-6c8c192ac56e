// Vue.js 应用主逻辑
const { createApp } = Vue;

// 创建Vue应用
const app = createApp({
    data() {
        return {
            // 原始故障数据
            originalFaultData: [],
            // 过滤后的故障数据
            filteredFaultData: [],
            // 筛选条件
            filters: {
                area: '',
                level: [],
                cause: '',
                keyword: ''
            },
            // 筛选选项
            areaOptions: [],
            causeOptions: [],
            // 统计信息
            statistics: {
                total: 0,
                critical: 0,
                warning: 0,
                vehicle: 0,
                human: 0
            },
            // 弹窗控制
            dialogVisible: false,
            selectedFault: null,
            // 加载状态
            loading: false
        };
    },
    
    mounted() {
        // 初始化应用
        this.initApp();
        // 设置全局引用
        window.vueApp = this;
    },
    
    methods: {
        // 初始化应用
        async initApp() {
            try {
                this.loading = true;
                
                // 初始化地图
                await window.mapManager.initMap();
                
                // 加载示例数据
                this.loadSampleData();
                
                console.log('应用初始化完成');
            } catch (error) {
                console.error('应用初始化失败:', error);
                this.$message.error('应用初始化失败');
            } finally {
                this.loading = false;
            }
        },

        // 加载示例数据
        loadSampleData() {
            // 使用需求文档中的实际数据
            this.originalFaultData = [
                {
                    id: 1,
                    area: '宾川县',
                    longitude: 100.451122,
                    latitude: 25.771616,
                    level: '汇聚层',
                    system: 'SPN 100G 环 宾川县 大营瑶草庄-宾川县州城',
                    time: '2025/1/1 11:50',
                    cause: '车辆挂断',
                    detail: '瑶草庄出局3.2km挖机挂断光缆'
                },
                {
                    id: 2,
                    area: '鹤庆县',
                    longitude: 100.157276,
                    latitude: 26.538180,
                    level: '汇聚层',
                    system: 'OTN 老平面 鹤庆新移动（东北环）-洱源牛街上站',
                    time: '2025/1/1 12:35',
                    cause: '车辆挂断',
                    detail: '新移动出局5.46km，附挂联通杆路超高车辆挂断'
                },
                {
                    id: 3,
                    area: '弥渡县',
                    longitude: 100.454857,
                    latitude: 25.336857,
                    level: '接入层',
                    system: 'PTN 弥渡县弥城双树村 6220设备脱管',
                    time: '2025/1/1 13:40',
                    cause: '人为破坏',
                    detail: '蔡庄出局3km自来水厂修管道夹断导致'
                },
                {
                    id: 4,
                    area: '洱源县',
                    longitude: 99.928195,
                    latitude: 26.136207,
                    level: '接入层',
                    system: 'PTN GE链路 洱源县茈碧湖镇下汉登-洱源县茈碧湖基站',
                    time: '2025/1/1 16:07',
                    cause: '车辆挂断',
                    detail: '下汉登基站出局1.97KM处超高车辆挂断'
                },
                {
                    id: 5,
                    area: '大理市',
                    longitude: 100.203212,
                    latitude: 25.593299,
                    level: '接入层',
                    system: 'PTN GE环 大理市下关龙泉新区华兴物业管理公司--大理市上村玉皇阁',
                    time: '2025/1/2 16:49',
                    cause: '车辆挂断',
                    detail: '玉皇阁基站出局277米车辆挂断光缆，拉预留熔纤恢复，重新挂高5米无遗留隐患'
                },
                {
                    id: 6,
                    area: '弥渡县',
                    longitude: 100.441907,
                    latitude: 25.512732,
                    level: '汇聚层',
                    system: 'OTN B平面 东环新桥北OTM-东环祥云县城南汇聚OTM',
                    time: '2025/1/3 8:45',
                    cause: '人为破坏',
                    detail: '祥云县新移动五楼传输机房出局22.55公里村民砍树砍断光缆，布放光缆熔纤恢复，现场无遗留隐患'
                },
                {
                    id: 7,
                    area: '洱源县',
                    longitude: 99.927652,
                    latitude: 26.136320,
                    level: '汇聚层',
                    system: 'SDH2.5G环 洱源县乔后-洱源县新移动',
                    time: '2025/1/3 16:42',
                    cause: '车辆挂断',
                    detail: '洱源老移动出局5.29KM车辆挂损光缆，修复光缆恢复，后期迁改挂高'
                },
                {
                    id: 8,
                    area: '永平县',
                    longitude: 99.628892,
                    latitude: 25.249673,
                    level: '接入层',
                    system: 'PTN GE环 永平县三村-永平县瓦绊',
                    time: '2025/1/4 14:45',
                    cause: '市政施工',
                    detail: '瓦绊出局3.3KM，高速施工挖机挖断光缆'
                },
                {
                    id: 9,
                    area: '祥云县',
                    longitude: 100.931431,
                    latitude: 25.631936,
                    level: '接入层',
                    system: 'PTN GE环 祥云县余食郎--祥云县东山乡外居苴',
                    time: '2025/1/6 14:04',
                    cause: '车辆挂断',
                    detail: '居苴基站出局8.4公里超高车辆挂断光缆熔纤恢复'
                }
            ];

            // 初始化筛选选项
            this.updateFilterOptions();
            
            // 应用初始筛选（显示所有数据）
            this.applyFilters();
            
            console.log('示例数据加载完成，共', this.originalFaultData.length, '条记录');
        },

        // 更新筛选选项
        updateFilterOptions() {
            // 提取唯一的区域选项
            this.areaOptions = [...new Set(this.originalFaultData.map(item => item.area))];
            
            // 提取唯一的故障原因选项
            this.causeOptions = [...new Set(this.originalFaultData.map(item => item.cause))];
        },

        // 应用筛选条件
        applyFilters() {
            let filtered = [...this.originalFaultData];

            // 区域筛选
            if (this.filters.area) {
                filtered = filtered.filter(item => item.area === this.filters.area);
            }

            // 故障级别筛选
            if (this.filters.level.length > 0) {
                filtered = filtered.filter(item => this.filters.level.includes(item.level));
            }

            // 故障原因筛选
            if (this.filters.cause) {
                filtered = filtered.filter(item => item.cause === this.filters.cause);
            }

            // 关键词搜索
            if (this.filters.keyword) {
                const keyword = this.filters.keyword.toLowerCase();
                filtered = filtered.filter(item => 
                    item.system.toLowerCase().includes(keyword) ||
                    item.detail.toLowerCase().includes(keyword)
                );
            }

            this.filteredFaultData = filtered;
            
            // 更新统计信息
            this.updateStatistics();
            
            // 更新地图标记
            window.mapManager.addMarkers(this.filteredFaultData);
            
            // 调整地图视野
            if (this.filteredFaultData.length > 0) {
                window.mapManager.fitView(this.filteredFaultData);
            }

            console.log('筛选完成，显示', this.filteredFaultData.length, '条记录');
        },

        // 重置筛选条件
        resetFilters() {
            this.filters = {
                area: '',
                level: [],
                cause: '',
                keyword: ''
            };
            this.applyFilters();
            this.$message.success('筛选条件已重置');
        },

        // 更新统计信息
        updateStatistics() {
            const data = this.filteredFaultData;
            
            this.statistics = {
                total: data.length,
                critical: data.filter(item => item.level === '汇聚层').length,
                warning: data.filter(item => item.level === '接入层').length,
                vehicle: data.filter(item => item.cause === '车辆挂断').length,
                human: data.filter(item => item.cause === '人为破坏').length
            };
        },

        // 显示故障详情
        showFaultDetail(fault) {
            this.selectedFault = fault;
            this.dialogVisible = true;
        },

        // 处理文件上传
        handleFileChange(file) {
            if (!file.raw) return;
            
            const fileName = file.raw.name.toLowerCase();
            
            if (fileName.endsWith('.csv')) {
                this.parseCsvFile(file.raw);
            } else {
                this.$message.warning('暂时只支持CSV格式文件');
            }
        },

        // 解析CSV文件
        parseCsvFile(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const csv = e.target.result;
                    const results = Papa.parse(csv, {
                        header: true,
                        skipEmptyLines: true,
                        encoding: 'UTF-8'
                    });
                    
                    if (results.errors.length > 0) {
                        console.error('CSV解析错误:', results.errors);
                        this.$message.error('CSV文件解析失败');
                        return;
                    }
                    
                    this.processCsvData(results.data);
                } catch (error) {
                    console.error('文件读取失败:', error);
                    this.$message.error('文件读取失败');
                }
            };
            reader.readAsText(file, 'UTF-8');
        },

        // 处理CSV数据
        processCsvData(csvData) {
            try {
                const processedData = csvData.map((row, index) => {
                    // 解析经纬度
                    const coords = this.parseCoordinates(row['故障系统（断点经纬度）'] || '');
                    
                    return {
                        id: index + 1,
                        area: row['区域'] || '',
                        longitude: coords.longitude,
                        latitude: coords.latitude,
                        level: row['故障级别'] || '',
                        system: row['故障段落'] || '',
                        time: row['故障发生时间'] || '',
                        cause: row['故障原因'] || '',
                        detail: row['故障详细原因备注'] || ''
                    };
                }).filter(item => item.longitude && item.latitude);
                
                if (processedData.length === 0) {
                    this.$message.warning('未找到有效的坐标数据');
                    return;
                }
                
                this.originalFaultData = processedData;
                this.updateFilterOptions();
                this.applyFilters();
                
                this.$message.success(`成功导入 ${processedData.length} 条故障数据`);
            } catch (error) {
                console.error('数据处理失败:', error);
                this.$message.error('数据处理失败');
            }
        },

        // 解析坐标字符串
        parseCoordinates(coordStr) {
            try {
                const lines = coordStr.split('\n');
                let longitude = 0, latitude = 0;
                
                lines.forEach(line => {
                    if (line.includes('经度：')) {
                        longitude = parseFloat(line.replace('经度：', '').trim());
                    } else if (line.includes('纬度：')) {
                        latitude = parseFloat(line.replace('纬度：', '').trim());
                    }
                });
                
                return { longitude, latitude };
            } catch (error) {
                console.error('坐标解析失败:', error);
                return { longitude: 0, latitude: 0 };
            }
        }
    }
});

// 使用Element Plus
app.use(ElementPlus);

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}

// 挂载应用
app.mount('#app');
