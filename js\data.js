// 数据处理逻辑模块
class DataProcessor {
    constructor() {
        this.rawData = [];
        this.processedData = [];
    }

    // 数据验证
    validateFaultData(data) {
        const requiredFields = ['area', 'longitude', 'latitude', 'level', 'system', 'time', 'cause'];
        
        return data.filter(item => {
            // 检查必需字段
            const hasRequiredFields = requiredFields.every(field => 
                item.hasOwnProperty(field) && item[field] !== null && item[field] !== undefined
            );
            
            // 检查坐标有效性
            const hasValidCoords = 
                typeof item.longitude === 'number' && 
                typeof item.latitude === 'number' &&
                item.longitude >= -180 && item.longitude <= 180 &&
                item.latitude >= -90 && item.latitude <= 90;
            
            // 检查是否在大理州范围内（大致范围）
            const inDaliRegion = 
                item.longitude >= 99.0 && item.longitude <= 101.5 &&
                item.latitude >= 24.5 && item.latitude <= 27.0;
            
            if (!hasRequiredFields) {
                console.warn('数据缺少必需字段:', item);
                return false;
            }
            
            if (!hasValidCoords) {
                console.warn('坐标数据无效:', item);
                return false;
            }
            
            if (!inDaliRegion) {
                console.warn('坐标不在大理州范围内:', item);
                return false;
            }
            
            return true;
        });
    }

    // 数据标准化
    normalizeData(data) {
        return data.map((item, index) => ({
            id: item.id || index + 1,
            area: String(item.area).trim(),
            longitude: Number(item.longitude),
            latitude: Number(item.latitude),
            level: String(item.level).trim(),
            system: String(item.system || '').trim(),
            time: this.formatTime(item.time),
            cause: String(item.cause).trim(),
            detail: String(item.detail || '').trim(),
            // 添加计算字段
            severity: this.calculateSeverity(item),
            category: this.categorizeByLocation(item)
        }));
    }

    // 时间格式化
    formatTime(timeStr) {
        if (!timeStr) return '';
        
        try {
            // 尝试解析不同的时间格式
            const formats = [
                /(\d{4})\/(\d{1,2})\/(\d{1,2})\s+(\d{1,2}):(\d{1,2})/,  // 2025/1/1 11:50
                /(\d{4})-(\d{1,2})-(\d{1,2})\s+(\d{1,2}):(\d{1,2})/,   // 2025-1-1 11:50
                /(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{1,2})/  // 1/1/2025 11:50
            ];
            
            for (const format of formats) {
                const match = String(timeStr).match(format);
                if (match) {
                    const [, year, month, day, hour, minute] = match;
                    return `${year}/${month.padStart(2, '0')}/${day.padStart(2, '0')} ${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`;
                }
            }
            
            return String(timeStr);
        } catch (error) {
            console.warn('时间格式化失败:', timeStr, error);
            return String(timeStr);
        }
    }

    // 计算故障严重程度
    calculateSeverity(item) {
        let score = 0;
        
        // 根据故障级别评分
        if (item.level === '汇聚层') {
            score += 3;
        } else if (item.level === '接入层') {
            score += 1;
        }
        
        // 根据故障原因评分
        const causeScores = {
            '车辆挂断': 2,
            '人为破坏': 3,
            '市政施工': 2,
            '自然灾害': 3,
            '设备故障': 1
        };
        
        score += causeScores[item.cause] || 1;
        
        // 根据影响范围评分（通过系统描述判断）
        const systemDesc = String(item.system || '').toLowerCase();
        if (systemDesc.includes('环') || systemDesc.includes('汇聚')) {
            score += 2;
        }
        
        return Math.min(score, 5); // 最高5分
    }

    // 根据位置分类
    categorizeByLocation(item) {
        const area = String(item.area);
        
        // 大理州主要区县分类
        const categories = {
            '核心区': ['大理市'],
            '北部': ['鹤庆县', '洱源县'],
            '南部': ['弥渡县', '巍山县'],
            '西部': ['永平县', '云龙县'],
            '东部': ['祥云县', '宾川县'],
            '山区': ['剑川县', '漾濞县', '南涧县']
        };
        
        for (const [category, areas] of Object.entries(categories)) {
            if (areas.some(a => area.includes(a))) {
                return category;
            }
        }
        
        return '其他';
    }

    // 数据统计分析
    analyzeData(data) {
        const analysis = {
            total: data.length,
            byLevel: {},
            byCause: {},
            byArea: {},
            byTime: {},
            severity: {
                high: 0,
                medium: 0,
                low: 0
            },
            trends: this.analyzeTrends(data)
        };
        
        data.forEach(item => {
            // 按级别统计
            analysis.byLevel[item.level] = (analysis.byLevel[item.level] || 0) + 1;
            
            // 按原因统计
            analysis.byCause[item.cause] = (analysis.byCause[item.cause] || 0) + 1;
            
            // 按区域统计
            analysis.byArea[item.area] = (analysis.byArea[item.area] || 0) + 1;
            
            // 按时间统计（按日期）
            const date = item.time.split(' ')[0];
            analysis.byTime[date] = (analysis.byTime[date] || 0) + 1;
            
            // 按严重程度统计
            if (item.severity >= 4) {
                analysis.severity.high++;
            } else if (item.severity >= 2) {
                analysis.severity.medium++;
            } else {
                analysis.severity.low++;
            }
        });
        
        return analysis;
    }

    // 趋势分析
    analyzeTrends(data) {
        const trends = {
            timePattern: {},
            causePattern: {},
            areaPattern: {}
        };
        
        // 时间趋势
        data.forEach(item => {
            const hour = parseInt(item.time.split(' ')[1]?.split(':')[0] || 0);
            const timeSlot = this.getTimeSlot(hour);
            trends.timePattern[timeSlot] = (trends.timePattern[timeSlot] || 0) + 1;
        });
        
        // 原因趋势
        const recentData = data.slice(-10); // 最近10条记录
        recentData.forEach(item => {
            trends.causePattern[item.cause] = (trends.causePattern[item.cause] || 0) + 1;
        });
        
        return trends;
    }

    // 获取时间段
    getTimeSlot(hour) {
        if (hour >= 6 && hour < 12) return '上午';
        if (hour >= 12 && hour < 18) return '下午';
        if (hour >= 18 && hour < 24) return '晚上';
        return '凌晨';
    }

    // 数据导出
    exportData(data, format = 'csv') {
        switch (format.toLowerCase()) {
            case 'csv':
                return this.exportToCsv(data);
            case 'json':
                return this.exportToJson(data);
            default:
                throw new Error('不支持的导出格式');
        }
    }

    // 导出为CSV
    exportToCsv(data) {
        const headers = [
            '序号', '区域', '经度', '纬度', '故障级别', 
            '故障段落', '故障时间', '故障原因', '详细原因', '严重程度'
        ];
        
        const rows = data.map(item => [
            item.id,
            item.area,
            item.longitude,
            item.latitude,
            item.level,
            item.system,
            item.time,
            item.cause,
            item.detail,
            item.severity
        ]);
        
        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
            .join('\n');
        
        return csvContent;
    }

    // 导出为JSON
    exportToJson(data) {
        return JSON.stringify(data, null, 2);
    }

    // 数据搜索
    searchData(data, query) {
        if (!query || query.trim() === '') {
            return data;
        }
        
        const searchTerm = query.toLowerCase().trim();
        
        return data.filter(item => {
            return Object.values(item).some(value => 
                String(value).toLowerCase().includes(searchTerm)
            );
        });
    }

    // 高级筛选
    advancedFilter(data, filters) {
        return data.filter(item => {
            // 时间范围筛选
            if (filters.timeRange && filters.timeRange.length === 2) {
                const itemTime = new Date(item.time);
                const [startTime, endTime] = filters.timeRange;
                if (itemTime < startTime || itemTime > endTime) {
                    return false;
                }
            }
            
            // 严重程度筛选
            if (filters.severity && filters.severity.length > 0) {
                const severityLevel = item.severity >= 4 ? 'high' : 
                                    item.severity >= 2 ? 'medium' : 'low';
                if (!filters.severity.includes(severityLevel)) {
                    return false;
                }
            }
            
            // 地理范围筛选
            if (filters.bounds) {
                const { north, south, east, west } = filters.bounds;
                if (item.latitude > north || item.latitude < south ||
                    item.longitude > east || item.longitude < west) {
                    return false;
                }
            }
            
            return true;
        });
    }
}

// 导出数据处理器实例
window.dataProcessor = new DataProcessor();
